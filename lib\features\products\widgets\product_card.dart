import 'package:flutter/material.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_strings.dart';
import '../../../models/product_model.dart';

class ProductCard extends StatelessWidget {
  final ProductModel product;
  final Function(String) onAction;

  const ProductCard({super.key, required this.product, required this.onAction});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shadowColor: AppColors.shadow,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: AppColors.cardGradient,
        ),
        child: Column(
          children: [
            // رأس البطاقة - اسم المنتج والفئة
            _buildCardHeader(),

            // محتوى البطاقة - المعلومات الأساسية
            _buildCardContent(),

            // شريط الإجراءات
            _buildActionBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildCardHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          // أيقونة الفئة
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getCategoryIcon(product.category),
              color: AppColors.textOnPrimary,
              size: 20,
            ),
          ),

          const SizedBox(width: 12),

          // اسم المنتج والفئة
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  product.categoryDisplayName,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // حالة المخزون
          _buildStockStatus(),
        ],
      ),
    );
  }

  Widget _buildCardContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // الصف الأول - الكود والباركود
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الكود',
                  product.code,
                  Icons.qr_code,
                  AppColors.info,
                ),
              ),
              if (product.hasBarcode) ...[
                const SizedBox(width: 16),
                Expanded(
                  child: _buildInfoItem(
                    'الباركود',
                    product.barcode!,
                    Icons.qr_code_2,
                    AppColors.secondary,
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 16),

          // الصف الثاني - الأسعار
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'سعر البيع',
                  '${product.price} ${AppStrings.currencySymbol}',
                  Icons.sell,
                  AppColors.success,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildInfoItem(
                  'سعر الشراء',
                  '${product.cost} ${AppStrings.currencySymbol}',
                  Icons.shopping_cart,
                  AppColors.warning,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // الصف الثالث - الكمية والوحدة
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الكمية المتوفرة',
                  '${product.quantity} ${product.unitDisplayName}',
                  Icons.inventory,
                  _getQuantityColor(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildInfoItem(
                  'الحد الأدنى',
                  '${product.minQuantity} ${product.unitDisplayName}',
                  Icons.warning,
                  AppColors.warning,
                ),
              ),
            ],
          ),

          // هامش الربح
          if (product.cost > 0) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getProfitMarginColor().withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: _getProfitMarginColor(), width: 1),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.trending_up,
                    color: _getProfitMarginColor(),
                    size: 16,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'هامش الربح: ${product.profitMargin.toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: _getProfitMarginColor(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            'تفاصيل',
            Icons.info_outline,
            AppColors.info,
            () => onAction('details'),
          ),
          _buildActionButton(
            'تعديل',
            Icons.edit,
            AppColors.warning,
            () => onAction('edit'),
          ),
          _buildActionButton(
            'بيع',
            Icons.sell,
            AppColors.success,
            () => onAction('sell'),
          ),
          _buildActionButton(
            'كمية',
            Icons.inventory_2,
            AppColors.primary,
            () => onAction('adjust_quantity'),
          ),
          _buildActionButton(
            'حذف',
            Icons.delete,
            AppColors.error,
            () => onAction('delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 6),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildStockStatus() {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    if (product.quantity == 0) {
      statusColor = AppColors.error;
      statusIcon = Icons.error_outline;
      statusText = 'نفذ';
    } else if (product.isLowStock) {
      statusColor = AppColors.warning;
      statusIcon = Icons.warning_amber_outlined;
      statusText = 'منخفض';
    } else {
      statusColor = AppColors.success;
      statusIcon = Icons.check_circle_outline;
      statusText = 'متوفر';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, color: statusColor, size: 16),
          const SizedBox(width: 4),
          Text(
            statusText,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: statusColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'devices':
        return Icons.medical_services;
      case 'consumables':
        return Icons.medical_information;
      case 'sterilization':
        return Icons.cleaning_services;
      case 'laboratory':
        return Icons.science;
      case 'general_supplies':
        return Icons.inventory;
      default:
        return Icons.category;
    }
  }

  Color _getQuantityColor() {
    if (product.quantity == 0) return AppColors.error;
    if (product.isLowStock) return AppColors.warning;
    return AppColors.success;
  }

  Color _getProfitMarginColor() {
    final margin = product.profitMargin;
    if (margin >= 50) return AppColors.success;
    if (margin >= 25) return AppColors.warning;
    return AppColors.error;
  }
}
